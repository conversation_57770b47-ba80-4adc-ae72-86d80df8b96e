from db import init_db
from mcp.server.fastmcp import FastMCP
from mcp_server.tools import setup_tools
from services.tool_registration_service import ToolRegistrationService


def create_server():
    """Create and configure the MCP server with all tools."""
    mcp = FastMCP("mcp-tools-server")

    # Initialize database
    init_db()

    # Setup tools
    tool_manager = setup_tools()

    # Register tools and namespaces in database
    registration_service = ToolRegistrationService()
    try:
        stats = registration_service.register_all_tools(tool_manager)
        print(f"📊 Tool registration stats: {stats}")
    except Exception as e:
        print(f"⚠️ Warning: Tool registration failed: {e}")

    # Register all tools in one go with explicit names
    for method_name, method in tool_manager.get_all_tool_methods().items():
        print(f"Registering tool: {method_name}")
        try:
            mcp.tool(name=method_name)(method)
            print(f"✅ Successfully registered: {method_name}")
        except Exception as e:
            print(f"❌ Failed to register {method_name}: {e}")

    return mcp, tool_manager


def main():
    print("Starting MCP Tools Server...")

    mcp, tool_manager = create_server()

    # Print summary
    registry = tool_manager.get_registry()
    discovered_count = len(tool_manager.get_discovered_tools())

    print(f"Registered {discovered_count} tool classes")
    print(f"Tools: {registry.list_tools()}")
    print(f"Methods: {registry.list_tool_methods()}")

    print("Starting FastMCP server...")
    mcp.run(transport="stdio")


if __name__ == "__main__":
    main()
