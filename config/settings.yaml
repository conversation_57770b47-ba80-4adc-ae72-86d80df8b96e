# Orchestra Template Engine Configuration
# This file contains the default configuration for the application.
# Values can be overridden by environment variables using the pattern:
# SECTION__KEY (e.g., DATABASE__HOST=localhost)

# Application Environment
environment: development

# Database Configuration
database:
  host: localhost
  port: 5432
  name: orchestra
  user: postgres
  password: ""  # Set via DB__PASSWORD environment variable
  driver: postgresql
  echo: false
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30
  pool_recycle: 3600

# API Configuration
api:
  title: "Orchestra Template Engine"
  description: "API for managing templates and rendering them with values"
  version: "1.0.0"
  host: "0.0.0.0"
  port: 8000
  debug: false
  reload: false
  workers: 1

# Logging Configuration
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: null  # Set to enable file logging
  max_file_size: 10485760  # 10MB
  backup_count: 5

# Security Configuration
security:
  secret_key: "your-secret-key-change-in-production"
  algorithm: "HS256"
  access_token_expire_minutes: 30
  cors_origins:
    - "*"
  cors_methods:
    - "*"
  cors_headers:
    - "*"

# Redis Configuration (for caching)
redis:
  host: localhost
  port: 6379
  db: 0
  password: null
  max_connections: 10

# Application-specific Settings
temp_dir: "/tmp/orchestra"
max_template_size: 10485760  # 10MB
template_cache_ttl: 3600  # 1 hour

# Environment-specific overrides
# These sections will be merged based on the 'environment' setting

# Development environment overrides
development:
  database:
    echo: true
  api:
    debug: true
    reload: true
  logging:
    level: DEBUG

# Staging environment overrides
staging:
  database:
    pool_size: 15
  api:
    workers: 2
  logging:
    level: INFO
    file_path: "/var/log/orchestra/app.log"

# Production environment overrides
production:
  database:
    echo: false
    pool_size: 20
    max_overflow: 30
  api:
    debug: false
    reload: false
    workers: 4
  logging:
    level: WARNING
    file_path: "/var/log/orchestra/app.log"
  security:
    cors_origins:
      - "https://yourdomain.com"
      - "https://api.yourdomain.com"
