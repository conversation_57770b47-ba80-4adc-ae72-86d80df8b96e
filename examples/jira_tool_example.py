#!/usr/bin/env python3
"""
jira_tool_example.py

Example script demonstrating how to use the Jira Tool.

This script shows how to:
1. Test connection to Jira
2. Get project information
3. Create a new ticket
4. Get a specific ticket
5. Search for tickets using JQL
6. Get comments and attachments

Author: <PERSON>
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.mcp_server.tools.jira_tool import JiraTool


async def main():
    """Demonstrate Jira tool functionality."""
    print("🎫 Jira Tool Example")
    print("=" * 50)
    
    # Initialize the tool
    tool = JiraTool()
    
    print(f"Tool: {tool.tool_name}")
    print(f"Description: {tool.tool_description}")
    print()
    
    # Test connection
    print("1. Testing Jira connection...")
    connection_result = await tool.test_connection()
    print(f"Connection result: {connection_result}")
    print()
    
    if not connection_result.get("success"):
        print("❌ Cannot connect to <PERSON><PERSON>. Please configure your Jira settings:")
        print("   - Set JIRA__BASE_URL environment variable")
        print("   - Set JIRA__USERNAME environment variable")
        print("   - Set JIRA__API_TOKEN environment variable")
        print()
        print("Example:")
        print("   export JIRA__BASE_URL=https://your-company.atlassian.net")
        print("   export JIRA__USERNAME=<EMAIL>")
        print("   export JIRA__API_TOKEN=your-api-token")
        return
    
    print("✅ Successfully connected to Jira!")
    print()

    # Example project key - you'll need to replace this with a real project
    example_project = "PROJ"
    example_ticket = "PROJ-123"

    # Get project information
    print(f"2. Getting project information: {example_project}")
    project_result = await tool.get_project_info(example_project)

    if project_result.get("success"):
        project = project_result["project"]
        print(f"   ✅ Found project: {project['name']} ({project['key']})")
        print(f"   📝 Description: {project['description']}")
        print(f"   👤 Lead: {project['lead']}")
        print(f"   🎯 Issue types: {[it['name'] for it in project['issue_types']]}")
        print(f"   🧩 Components: {[c['name'] for c in project['components']]}")

        # Use the first available issue type for creating a ticket
        available_issue_types = [it['name'] for it in project['issue_types']]
        if available_issue_types:
            issue_type_for_creation = available_issue_types[0]
        else:
            issue_type_for_creation = "Task"  # fallback
    else:
        print(f"   ❌ Could not get project info: {project_result.get('error')}")
        issue_type_for_creation = "Task"  # fallback
    print()

    # Create a new ticket (example)
    print(f"3. Creating a new ticket in project: {example_project}")
    create_result = await tool.create_ticket(
        project_key=example_project,
        summary="Example ticket created by Jira Tool",
        issue_type=issue_type_for_creation,
        description="This is an example ticket created to demonstrate the Jira Tool functionality.",
        priority="Medium",
        labels=["example", "automation"]
    )

    if create_result.get("success"):
        new_ticket = create_result["ticket"]
        print(f"   ✅ Created ticket: {new_ticket['key']}")
        print(f"   🔗 URL: {new_ticket['url']}")
        example_ticket = new_ticket['key']  # Use the newly created ticket for further examples
    else:
        print(f"   ❌ Could not create ticket: {create_result.get('error')}")
        print("   (This might be due to insufficient permissions or missing project)")
    print()
    
    # Get a specific ticket
    print(f"4. Getting ticket: {example_ticket}")
    ticket_result = await tool.get_ticket(example_ticket)

    if ticket_result.get("success"):
        ticket = ticket_result["ticket"]
        print(f"   ✅ Found ticket: {ticket['key']}")
        print(f"   📝 Summary: {ticket['summary']}")
        print(f"   📊 Status: {ticket['status']}")
        print(f"   👤 Assignee: {ticket['assignee']}")
        print(f"   🔗 URL: {ticket['url']}")
    else:
        print(f"   ❌ Could not get ticket: {ticket_result.get('error')}")
    print()

    # Search for tickets
    print("5. Searching for tickets...")
    search_jql = "status != Done ORDER BY updated DESC"
    search_result = await tool.search_tickets(search_jql, max_results=5)

    if search_result.get("success"):
        results = search_result["search_results"]
        print(f"   ✅ Found {results['total']} tickets (showing {len(results['tickets'])})")

        for ticket in results["tickets"]:
            print(f"   🎫 {ticket['key']}: {ticket['summary']}")
            print(f"      Status: {ticket['status']}, Assignee: {ticket['assignee']}")
    else:
        print(f"   ❌ Search failed: {search_result.get('error')}")
    print()

    # Get comments for the example ticket
    print(f"6. Getting comments for ticket: {example_ticket}")
    comments_result = await tool.get_ticket_comments(example_ticket, max_results=3)

    if comments_result.get("success"):
        comments = comments_result["comments"]
        print(f"   ✅ Found {comments['total']} comments (showing {len(comments['comments'])})")

        for comment in comments["comments"]:
            print(f"   💬 {comment['author']['name']}: {comment['body'][:100]}...")
            print(f"      Created: {comment['created']}")
    else:
        print(f"   ❌ Could not get comments: {comments_result.get('error')}")
    print()

    # Get attachments for the example ticket
    print(f"7. Getting attachments for ticket: {example_ticket}")
    attachments_result = await tool.get_ticket_attachments(example_ticket)
    
    if attachments_result.get("success"):
        attachments = attachments_result["attachments"]
        print(f"   ✅ Found {len(attachments)} attachments")
        
        for attachment in attachments:
            size_mb = attachment['size'] / (1024 * 1024)
            print(f"   📎 {attachment['filename']} ({size_mb:.2f} MB)")
            print(f"      Type: {attachment['mime_type']}")
            print(f"      Author: {attachment['author']['name']}")
    else:
        print(f"   ❌ Could not get attachments: {attachments_result.get('error')}")
    print()
    
    print("🎉 Jira tool example completed!")


if __name__ == "__main__":
    # Check if basic configuration is available
    if not os.getenv("JIRA__BASE_URL"):
        print("⚠️  No Jira configuration found.")
        print("   This example will test the tool but won't be able to connect to Jira.")
        print("   To test with real Jira data, set the following environment variables:")
        print("   - JIRA__BASE_URL")
        print("   - JIRA__USERNAME") 
        print("   - JIRA__API_TOKEN")
        print()
    
    asyncio.run(main())
